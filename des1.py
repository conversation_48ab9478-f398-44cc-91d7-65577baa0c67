from Crypto.Cipher import DES
from Crypto.Util.Padding import pad, unpad

# Asking the user to enter an 8-character key
key_input = input("Enter an 8-character secret key: ")

# Validating the key length
if len(key_input) != 8:
    print("Key should be exactly 8 characters long.")
    exit()  # Exit ONLY if the key is invalid

# Converting key to bytes
key = key_input.encode()

# Getting the message from the user
message = input("Enter the message to encrypt: ")
print("\nOriginal Message:", message)

# Convert message to bytes
message_bytes = message.encode()

# Pad message to a multiple of 8 bytes
padded_message = pad(message_bytes, DES.block_size)

# Create DES cipher in ECB mode
cipher = DES.new(key, DES.MODE_ECB)

# Encrypt the message
encrypted_message = cipher.encrypt(padded_message)
print("Encrypted Message (Hex):", encrypted_message.hex())

# Decrypt the message
decrypted_padded = cipher.decrypt(encrypted_message)

# Unpad and decode
decrypted_message = unpad(decrypted_padded, DES.block_size)
print("Decrypted Message:", decrypted_message.decode())
