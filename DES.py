

# Initial Permutation Table
IP = [58, 50, 42, 34, 26, 18, 10, 2,
      60, 52, 44, 36, 28, 20, 12, 4,
      62, 54, 46, 38, 30, 22, 14, 6,
      64, 56, 48, 40, 32, 24, 16, 8,
      57, 49, 41, 33, 25, 17, 9, 1,
      59, 51, 43, 35, 27, 19, 11, 3,
      61, 53, 45, 37, 29, 21, 13, 5,
      63, 55, 47, 39, 31, 23, 15, 7]

# Expansion (E) Table
E = [32, 1, 2, 3, 4, 5,
     4, 5, 6, 7, 8, 9,
     8, 9, 10, 11, 12, 13,
     12, 13, 14, 15, 16, 17,
     16, 17, 18, 19, 20, 21,
     20, 21, 22, 23, 24, 25,
     24, 25, 26, 27, 28, 29,
     28, 29, 30, 31, 32, 1]

# Permutation (P-box)
P = [16, 7, 20, 21,
     29, 12, 28, 17,
     1, 15, 23, 26,
     5, 18, 31, 10,
     2, 8, 24, 14,
     32, 27, 3, 9,
     19, 13, 30, 6,
     22, 11, 4, 25]


PC1 = [57, 49, 41, 33, 25, 17, 9,
       1, 58, 50, 42, 34, 26, 18,
       10, 2, 59, 51, 43, 35, 27,
       19, 11, 3, 60, 52, 44, 36,
       63, 55, 47, 39, 31, 23, 15,
       7, 62, 54, 46, 38, 30, 22,
       14, 6, 61, 53, 45, 37, 29,
       21, 13, 5, 28, 20, 12, 4]


PC2 = [14, 17, 11, 24, 1, 5,
       3, 28, 15, 6, 21, 10,
       23, 19, 12, 4, 26, 8,
       16, 7, 27, 20, 13, 2,
       41, 52, 31, 37, 47, 55,
       30, 40, 51, 45, 33, 48,
       44, 49, 39, 56, 34, 53,
       46, 42, 50, 36, 29, 32]

def permute(bits, table):
    """Apply permutation table to bits"""
    return ''.join(bits[i - 1] for i in table)

def str_to_bin(text):
    """Convert string to binary"""
    return ''.join(format(ord(c), '08b') for c in text)

def bin_to_str(bin_text):
    """Convert binary to string"""
    chars = [bin_text[i:i+8] for i in range(0, len(bin_text), 8)]
    return ''.join(chr(int(b, 2)) for b in chars)


plaintext = "ABCDEFGH"
binary_plaintext = str_to_bin(plaintext)
print(f"Original Binary:\n{binary_plaintext}\n")


ip_output = permute(binary_plaintext, IP)
print(f"After Initial Permutation:\n{ip_output}\n")


L0, R0 = ip_output[:32], ip_output[32:]
print(f"L0: {L0}")
print(f"R0: {R0}\n")


expanded_R0 = permute(R0, E)
print(f"Expanded R0 (E-box):\n{expanded_R0}\n")


dummy_32bit = '********************************'
pbox_output = permute(dummy_32bit, P)
print(f"After P-box Permutation:\n{pbox_output}\n")


key = "13345779"
binary_key = str_to_bin(key)
print(f"Original Key Binary:\n{binary_key}\n")


pc1_output = permute(binary_key, PC1)
print(f"After Permuted Choice 1 (PC-1):\n{pc1_output}\n")


C0, D0 = pc1_output[:28], pc1_output[28:]
print(f"C0: {C0}")
print(f"D0: {D0}\n")


combined_CD = C0 + D0
pc2_output = permute(combined_CD, PC2)
print(f"After Permuted Choice 2 (PC-2):\n{pc2_output}")
