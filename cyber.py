def caesar_cipher(text, shift, encrypt=True):

    result = ""
    
    # Reverse shift for decryption
    if not encrypt:
        shift = -shift
    
    for char in text:
        if char.isupper():
            # Shift uppercase letters
            result += chr((ord(char) + shift - 65) % 26 + 65)
        elif char.islower():
            # Shift lowercase letters
            result += chr((ord(char) + shift - 97) % 26 + 97)
        else:
            # Leave other characters unchanged
            result += char
    
    return result

def get_valid_shift():
    """Get a valid shift value between 1 and 25 from user"""
    while True:
        try:
            shift = int(input("Enter shift value (1-25): "))
            if 1 <= shift <= 25:
                return shift
            print("Shift must be between 1 and 25")
        except ValueError:
            print("Please enter a valid number")

def main():
    print("Caesar Cipher Program")
    print("1. Encrypt")
    print("2. Decrypt")
    
    while True:
        choice = input("Choose option (1/2): ")
        if choice in ['1', '2']:
            break
        print("Please enter 1 or 2")
    
    message = input("Enter your message: ")
    shift = get_valid_shift()
    
    if choice == '1':
        result = caesar_cipher(message, shift)
        print(f"\nEncrypted message: {result}")
    else:
        result = caesar_cipher(message, shift, encrypt=False)
        print(f"\nDecrypted message: {result}")

if __name__ == "__main__":
    main()